// Common component props
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

// Size variants
export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl'

// Color variants
export type ColorVariant = 
  | 'primary' 
  | 'secondary' 
  | 'accent' 
  | 'destructive' 
  | 'success' 
  | 'warning' 
  | 'info' 
  | 'muted'

// Button variants
export type ButtonVariant = 
  | 'primary' 
  | 'secondary' 
  | 'outline' 
  | 'ghost' 
  | 'link'

// Input validation states
export type ValidationState = 'default' | 'error' | 'success' | 'warning'

// Alert/Toast types
export type AlertType = 'info' | 'success' | 'warning' | 'error'

// Modal/Dialog types
export interface DialogProps extends BaseComponentProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  title?: string
  description?: string
}

// Form field props
export interface FormFieldProps extends BaseComponentProps {
  label?: string
  description?: string
  error?: string
  required?: boolean
  disabled?: boolean
}

// Loading state
export interface LoadingProps {
  loading?: boolean
  loadingText?: string
}

// Icon props
export interface IconProps extends BaseComponentProps {
  size?: Size
  color?: string
}
