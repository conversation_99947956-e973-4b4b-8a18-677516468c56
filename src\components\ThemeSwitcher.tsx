import React from 'react'
import { <PERSON>, <PERSON>, Monitor } from 'lucide-react'
import { useTheme } from '../hooks/use-theme'
import { Button } from './ui/Button'
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from './ui/Dialog'
import { RadioGroupField } from './ui/RadioGroup'

export function ThemeSwitcher() {
  const { theme, setTheme } = useTheme()
  const [open, setOpen] = React.useState(false)

  const themeOptions = [
    {
      value: 'light',
      label: 'Light',
      description: 'Light mode theme',
    },
    {
      value: 'dark',
      label: 'Dark',
      description: 'Dark mode theme',
    },
    {
      value: 'system',
      label: 'System',
      description: 'Follow system preference',
    },
  ]

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return <Sun className="h-4 w-4" />
      case 'dark':
        return <Moon className="h-4 w-4" />
      default:
        return <Monitor className="h-4 w-4" />
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon">
          {getThemeIcon()}
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Theme Settings</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <RadioGroupField
            value={theme}
            onValueChange={(value) => {
              setTheme(value as 'light' | 'dark' | 'system')
              setOpen(false)
            }}
            options={themeOptions}
            label="Choose your theme"
            description="Select the theme for the application"
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}
