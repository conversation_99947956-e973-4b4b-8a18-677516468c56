import React from 'react'
import { Loader2 } from 'lucide-react'
import { cn } from '../../lib/utils'
import { type Size } from '../../types'

const spinnerSizes = {
  xs: 'h-3 w-3',
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12',
}

export interface SpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: Size
  text?: string
}

const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, size = 'md', text, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('flex items-center justify-center gap-2', className)}
        {...props}
      >
        <Loader2 className={cn('animate-spin', spinnerSizes[size])} />
        {text && <span className="text-sm text-muted-foreground">{text}</span>}
      </div>
    )
  }
)

Spinner.displayName = 'Spinner'

export { Spinner }
