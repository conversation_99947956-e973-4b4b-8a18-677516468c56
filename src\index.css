@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=JetBrains+Mono:wght@100..800&display=swap');
@import "tailwindcss";

/* Design Tokens - CSS Variables */
:root {
  /* Light mode colors */
  --color-primary: 59 130 246; /* blue-500 */
  --color-secondary: 107 114 128; /* gray-500 */
  --color-accent: 168 85 247; /* purple-500 */
  --color-background: 255 255 255; /* white */
  --color-foreground: 15 23 42; /* slate-900 */
  --color-surface: 248 250 252; /* slate-50 */
  --color-border: 226 232 240; /* slate-200 */
  --color-muted: 241 245 249; /* slate-100 */
  --color-muted-foreground: 100 116 139; /* slate-500 */
  --color-destructive: 239 68 68; /* red-500 */
  --color-destructive-foreground: 255 255 255; /* white */
  --color-success: 34 197 94; /* green-500 */
  --color-success-foreground: 255 255 255; /* white */
  --color-warning: 245 158 11; /* amber-500 */
  --color-warning-foreground: 255 255 255; /* white */
  --color-info: 14 165 233; /* sky-500 */
  --color-info-foreground: 255 255 255; /* white */
}

/* Dark mode colors */
.dark {
  --color-primary: 96 165 250; /* blue-400 */
  --color-secondary: 156 163 175; /* gray-400 */
  --color-accent: 196 181 253; /* purple-300 */
  --color-background: 2 6 23; /* slate-950 */
  --color-foreground: 248 250 252; /* slate-50 */
  --color-surface: 15 23 42; /* slate-900 */
  --color-border: 51 65 85; /* slate-700 */
  --color-muted: 30 41 59; /* slate-800 */
  --color-muted-foreground: 148 163 184; /* slate-400 */
  --color-destructive: 248 113 113; /* red-400 */
  --color-destructive-foreground: 15 23 42; /* slate-900 */
  --color-success: 74 222 128; /* green-400 */
  --color-success-foreground: 15 23 42; /* slate-900 */
  --color-warning: 251 191 36; /* amber-400 */
  --color-warning-foreground: 15 23 42; /* slate-900 */
  --color-info: 56 189 248; /* sky-400 */
  --color-info-foreground: 15 23 42; /* slate-900 */
}

/* Global styles */
* {
  border-color: rgb(var(--color-border));
  transition: border-color 0.2s ease-in-out;
}

html {
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid rgb(var(--color-primary));
  outline-offset: 2px;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(var(--color-muted));
}

::-webkit-scrollbar-thumb {
  background: rgb(var(--color-border));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--color-muted-foreground));
}