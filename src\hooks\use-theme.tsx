import React, { createContext, useContext, useEffect, useState } from 'react'
import { Theme } from '../types'
import { getSystemTheme, isBrowser } from '../lib/utils'

interface ThemeContextType {
  theme: Theme
  setTheme: (theme: Theme) => void
  actualTheme: 'light' | 'dark'
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

interface ThemeProviderProps {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
}

export function ThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = 'ui-theme',
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(() => {
    if (!isBrowser) return defaultTheme
    return (localStorage.getItem(storageKey) as Theme) || defaultTheme
  })

  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>(() => {
    if (!isBrowser) return 'light'
    if (theme === 'system') return getSystemTheme()
    return theme === 'dark' ? 'dark' : 'light'
  })

  useEffect(() => {
    if (!isBrowser) return

    const root = window.document.documentElement
    root.classList.remove('light', 'dark')

    if (theme === 'system') {
      const systemTheme = getSystemTheme()
      root.classList.add(systemTheme)
      setActualTheme(systemTheme)
    } else {
      root.classList.add(theme)
      setActualTheme(theme === 'dark' ? 'dark' : 'light')
    }
  }, [theme])

  useEffect(() => {
    if (!isBrowser) return

    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      if (theme === 'system') {
        const newTheme = e.matches ? 'dark' : 'light'
        setActualTheme(newTheme)
        const root = window.document.documentElement
        root.classList.remove('light', 'dark')
        root.classList.add(newTheme)
      }
    }

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', handleSystemThemeChange)

    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange)
  }, [theme])

  const value = {
    theme,
    setTheme: (theme: Theme) => {
      if (isBrowser) {
        localStorage.setItem(storageKey, theme)
      }
      setTheme(theme)
    },
    actualTheme,
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)

  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }

  return context
}
